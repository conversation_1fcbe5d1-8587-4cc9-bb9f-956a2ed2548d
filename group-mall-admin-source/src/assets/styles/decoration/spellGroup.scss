// 拼团

@import "../mixins/mixins";

@include b(spellpage) {
  .el-radio {
    margin-right: 20px;
  }

  @include e(addmart) {
    margin-top: 7px;
  }
}

@include b(spellpre) {
  background-color: #f5f5f5;
  box-sizing: border-box;
  position: relative;

  @include e(poster) {
    display: inline-block;
    width: 100%;
    height: 100px;
    display: flex;
    justify-content: center;
    align-items: center;
    background-color: #e9f7fd;
    border-radius: 3px;
    margin-top: 7px;

    img {
      display: inline-block;
      width: 44px;
      height: 46px;
    }
  }

  @include e(header) {
    color: #333;
    height: 50px;
    line-height: 50px;
    box-sizing: border-box;
    background-color: #fff;
    width: 100%;
    position: absolute;
    left: 0;
    top: 0;
    z-index: 2;

    @include m(title) {
      font-size: 18px;
      font-weight: 700;
    }

    @include m(more) {
      font-size: 14px;
      font-weight: 400;
      float: right;
    }
  }

  @include e(headtemp) {
    height: 50px;
    line-height: 50px;
    width: 100%;
  }

  @include e(goods) {
    box-sizing: border-box;


    .spellpre__goods--box {
      position: relative;
      background-color: #fff;
      overflow: hidden;
    }

    @include m(boxCP) {
      box-shadow: 0 0 4px rgba(0, 0, 0, 0.2);
    }

    @include m(boxSW) {
      border: 1px solid #f5f5f5;
    }

    @include m(angle) {
      border-radius: 7px;
    }

    @include m(corners) {
      border-radius: 0;
    }

    @include m(nameF) {
      font-weight: 400;
    }

    @include m(nameB) {
      font-weight: 600;
    }

    @include m(cart1) {
      float: right;
      height: 28px;
      width: 28px;
      background-color: rgba(252, 98, 63, 1);
      box-sizing: border-box;
      border-radius: 50%;
      display: flex;
      justify-content: center;
      align-items: center;
      margin-top: 5px;

      img {
        display: inline-block;
        width: 19px;
        height: 19px;
      }
    }

    @include m(cart) {
      float: right;
      height: 25px;
      width: 25px;
      box-sizing: border-box;
      border-radius: 50%;
      display: flex;
      justify-content: center;
      align-items: center;
      margin-top: 10px;
    }

    @include m(cart1) {
      background-color: #fff;

      img {
        display: inline-block;
        width: 24px;
        height: 24px;
      }
    }

    @include m(cart2) {
      background:linear-gradient(164deg,rgba(243,243,243,1),rgba(229,56,46,1),rgba(253,78,38,1));
      box-shadow:0px 2px 7px 0px rgba(255,14,0,0.27);
      border-radius:50%;

      img {
        display: inline-block;
        width: 20px;
        height: 20px;
        left: 2px;
        top: 1px;
      }
    }

    @include m(cart3) {
      border: 1px solid rgba(252, 98, 63, 1);
      width: auto;
      padding: 0 5px;
      color: rgba(252, 98, 63, 1);
      font-size: 12px;
      border-radius: 12px;
      height: 22px;
      line-height: 22px;
    }

    @include m(cart4) {
      border: 1px solid rgba(252, 98, 63, 1);
      background-color: rgba(252, 98, 63, 1);
      width: auto;
      padding: 0 5px;
      color: #fff;
      font-size: 12px;
      border-radius: 12px;
      height: 22px;
      line-height: 22px;
    }

    @include m(coner) {
      position: absolute;
  
      img, span {
        display: block;
        width: 100%;
        height: 100%;
        position: absolute;
      }
    }
  
    @include m(coner1) {
      left: -1px;
      top: 6px;
      width: 38px;
      height: 22px;
    }
  
    @include m(coner2) {
      left: 0px;
      top: 0px;
      width: 38px;
      height: 41px;
    }
  
    @include m(coner3) {
      left: 8px;
      top: 7px;
      width: 42px;
      height: 21px;
    }

    @include m(delivery) {
      span {
        color: #FA6454;
        font-size: 12px;
        font-weight: 400;
        display: inline-block;
        background-color: rgba(250, 91, 74, .2);
        border-radius: 17px;
        padding: 2px 8px;
      }
    }

    @include m(footer) {
      .timebox {
        display: inline-block;
        height: 20px;
      }

      .tip {
        display: inline-block;
        height: 20px;
        line-height: 20px;
        color: #8B8B8B;
        font-size: 13px;
        margin-right: 4px;
        margin-top: 1px;
      }
    }

    .time_flex {
      width: 110px;
      height: 20px;
      display: inline-block;
      color: #2F2F2F;
      font-size: 9px;

      span {
        display: inline-block;
        width: 12px;
        height: 14px;
        line-height: 14px;
        text-align: center;
        color: #fff;
        background-color: #2F2F2F;
        border-radius: 3px;
        margin: 0px 1px;
      }
    }

    .time-info {
      float: right;
      height: 20px;
      line-height: 20px;
      color: #A3A3A3;
      font-size: 11px;
      margin-left: 8px;
      margin-top: 1px;
    }
  
    .user__icon {
      float: right;
      height: 20px;
      width: 60px;
      position: relative;
      margin-top: 1px;

      span {
        display: inline-block;
        width: 20px;
        height: 20px;
        border-radius: 50%;
        border: 1px solid rgba(255, 255, 255, 1);
        background-color: #D1D1D1;
        top: 0;
        position: absolute;

        img {
          width: 20px;
          height: 20px;
          border-radius: 50%;
        }
      }

      .icon_1 {
        right: 0;
        z-index: 1;
      }
      .icon_2 {
        right: 10px;
        z-index: 2;
      }
      .icon_3 {
        right: 20px;
        z-index: 3;
      }
    }
  }

  @include e(goodsL) {
    box-sizing: border-box;

    .spellpre__goods--box {
      box-sizing: border-box;
      padding: 10px;
    }

    @include m(content) {
      display: flex;
    }

    @include m(box) {
      display: flex;
      box-sizing: border-box;
      overflow: hidden;
      position: relative;
    }

    @include m(icon) {
      width: 138px;
      height: 138px;

      .ipic {
        display: inline-block;
        width: 138px;
        height: 138px;
        display: flex;
        justify-content: center;
        align-items: center;
        background-color: #e9f7fd;
        border-radius: 3px;

        img {
          display: inline-block;
          width: 44px;
          height: 46px;
        }
      }
    }

    .spellpre__goods--coner1 {
      left: 10px;
      top: 20px;
    }

    .spellpre__goods--coner2 {
      left: 10px;
      top: 10px;
    }

    .spellpre__goods--coner3 {
      left: 10px;
      top: 10px;
    }

    @include m(info) {
      flex: 1;
      box-sizing: border-box;
      margin-left: 10px;
      position: relative;
    }

    @include m(name) {
      text-overflow: -o-ellipsis-lastline;
      overflow: hidden;
      text-overflow: ellipsis;
      display: -webkit-box;
      -webkit-line-clamp: 2;
      line-clamp: 2;
      -webkit-box-orient: vertical;
      font-size: 15px;
      line-height: 20px;
      color: #0E0E0E;
      box-sizing: border-box;
      padding: 8px 0;
      max-height: 48px;
    }

    @include m(buy) {
      width: 100%;
      height: 65px;
      position: absolute;
      bottom: 0;
      left: 0;
      box-sizing: border-box;
      padding-top: 20px;

      .spellpre__goods--cart {
        margin-top: 0px;
      }
    }

    @include m(prinum) {
      display: inline-block;
      font-size: 19px;
      font-weight: 400;
      color: #EC342C;
    }

    @include m(guaid) {
      font-size: 12px;
      color: #AAAAAA;
      display: inline-block;
    }

    @include m(footer) {
      margin-top: 12px;
      height: 20px;
      line-height: 20px;
      position: relative;
      box-sizing: border-box;

      .tip {
        vertical-align: middle;
      }

      .time-info {
        margin-top: 1px;
      }
    }
  }

  @include e(goodsB) {
    box-sizing: border-box;

    @include m(box) {
      box-sizing: border-box;
      overflow: hidden;
      position: relative;
    }

    @include m(icon) {
      width: 100%;
      height: 175px;

      .ipic {
        display: inline-block;
        width: 100%;
        height: 175px;
        background-color: rgba(233, 247, 253, 1);
        display: flex;
        justify-content: center;
        align-items: center;

        img {
          display: inline-block;
          width: 44px;
          height: 46px;
        }
      }
    }

    .detail__box {
      box-sizing: border-box;
      padding: 0 8px;
    }

    @include m(name) {
      text-overflow: ellipsis;
      overflow: hidden;
      white-space: nowrap;
      font-size: 15px;
      line-height: 32px;
      height: 32px;
      color: #0E0E0E;
    }

    @include m(buy) {
      width: 100%;
      height: 40px;
      line-height: 40px;
      box-sizing: border-box;
    }

    @include m(prinum) {
      display: inline-block;
      font-size: 19px;
      font-weight: 500;
      color: #EC342C;
      height: 40px;
      line-height: 40px;
    }

    @include m(guaid) {
      font-size: 12px;
      color: #AAAAAA;
      display: inline-block;
      margin-left: 5px;
      height: 40px;
      line-height: 40px;
    }

    @include m(info) {
      box-sizing: border-box;
      height: 35px;
      position: relative;
    }
  }
}
