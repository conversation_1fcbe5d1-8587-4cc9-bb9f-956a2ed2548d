.el-tabs__active-bar.is-top {
  width: 24px !important;
  height: 3px !important;
  // background: #2E99F3;
  background: #2e99f3;
  border-radius: 2px;
}

.el-tabs__nav-wrap::after {
  background-color: #f7f7f7;
}

.el-button-group.fix {
  .el-button--primary {
    border-color: #ecf5ff !important;
  }

  .dropdown__fix {
    padding: 0;

    &.more {
      &::before {
        content: "";
        width: 1px;
        height: 12px;
        background: rgba(46, 153, 243, 1);
        opacity: 0.46;
        position: absolute;
        top: 10px;
        right: 34px;
      }
    }

    &--more {
      display: inline-block;
      width: 32px;
      height: 30px;
      position: relative;
      line-height: 24px;
      // top: -4px;
      color: #2e99f3;
    }
    &:hover {
      color: #fff;
      span {
        color: #fff;
      }
    }
  }
}
