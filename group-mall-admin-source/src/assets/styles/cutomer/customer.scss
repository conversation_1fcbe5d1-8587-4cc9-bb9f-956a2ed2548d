@import "../variable";
@import "../mixins/mixins";
@import "../mixins/utils.scss";

@include b(customer) {
  position: relative;
  margin-bottom: 40px;

  .pointer {
    cursor: pointer;
  }

  @include e(dataForm) {
    background-color: #f6f8fa;
    padding: 15px;
  }
  @include e(filterForm) {
    margin: 30px 0 10px 0;
  }
}

@include b(pickpage) {

  @include e(head) {

    @include m(search) {
      width: 246px;
      float: right;
    }

    @include m(type) {
      float: right;
      width: 150px;
      margin-right: 15px;
    }
  }
}

@include b(filter) {
  margin: 20px 0px;
  @include e(right) {

    @include m(select) {
      margin-right: 10px;
    }
  }
}

@include b(info) {
  margin: 10px;
  display: flex;
  text-align: left;
  vertical-align: center;

  @include e(img) {
    width: 40px;
    height: 40px;
    margin-top: 5px;
    border-radius: 4px;
  }
  @include e(msg) {
    // margin-top: -5px;
    padding-left: 10px;
    @include m(text) {
      display: flex;
      flex-wrap: wrap;
    }
  }
}

@include b(form) {
  transition: all 0.3s ease 0s;
  transform-origin: left top;
  overflow: hidden;
  position: relative;

  &.show {
    height: 260px;
  }

  &.hide {
    height: 50px;

    .form__btn {
      width: 940px;
      height: 50px;
      background: #f9f9f9;
      line-height: 50px;
      // margin-top: 20px
    }
  }

  @include e(btn) {
    width: 100%;
    position: absolute;
    bottom: 0;
    text-align: center;
    padding-bottom: 20px;

    span {
      cursor: pointer;
    }
  }
}

@include b(form) {
  transform-origin: left top;
  overflow: hidden;
  position: relative;
  transition: all 0.3s ease 0s;

  &.show {
    height: 300px;
    margin-bottom: 20px;
  }

  &.hide {
    margin-bottom: 20px;
    height: 50px;

    .form__btn {
      width: 940px;
      height: 50px;
      background: #f9f9f9;
      line-height: 50px;
      // margin-top: 20px
    }
  }

  @include e(btn) {
    width: 100%;
    position: absolute;
    bottom: 0;
    text-align: center;
    padding-bottom: 20px;

    span {
      cursor: pointer;
    }
  }
}

@include b(form2) {
  transform-origin: left top;
  overflow: hidden;
  position: relative;
  transition: all 0.3s ease 0s;

  &.show {
    height: 370px;
    margin-bottom: 20px;
  }

  &.hide {
    margin-bottom: 20px;
    height: 50px;

    .form__btn {
      width: 940px;
      height: 50px;
      background: #f9f9f9;
      line-height: 50px;
      // margin-top: 20px
    }
  }

  @include e(btn) {
    width: 100%;
    position: absolute;
    bottom: 0;
    text-align: center;
    padding-bottom: 20px;

    span {
      cursor: pointer;
    }
  }
}

.m__table--item {
  text-align: center;
}

.PageManage {
  position: fixed;
  width: 980px;
  background: #ffffff;
  z-index: 9999;
  padding: 10px;
  bottom: 10px;
  z-index: 100;
}
