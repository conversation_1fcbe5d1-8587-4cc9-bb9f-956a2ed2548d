/*边距（padding  pt-  pb-  pr-  px-  py-）*/

.p-none {
  padding: 0;
}

.pl-10 {
  padding-left: .625rem;
}

.pl-20 {
  padding-left: 1.25rem;
}

.pl-30 {
  padding-left: 1.875rem;
}

.pl-40 {
  padding-left: 2.5rem;
}

.pr-10 {
  padding-right: .625rem;
}

.pr-20 {
  padding-right: 1.25rem;
}

.pr-30 {
  padding-right: 1.875rem;
}

.pr-40 {
  padding-right: 2.5rem;
}

.pr-50 {
  padding-right: 3.125rem;
}

.pr-60 {
  padding-right: 3.75rem;
}

.pt-10 {
  padding-top: .625rem;
}

.pt-16 {
  padding-top: 1rem;
}

.pt-20 {
  padding-top: 1.25rem;
}

.pt-30 {
  padding-top: 1.875rem;
}

.pt-40 {
  padding-top: 2.5rem;
}

.pb-10 {
  padding-bottom: .625rem;
}

.pb-1em {
  padding-bottom: 1rem;
}

.pb-20 {
  padding-bottom: 1.25rem;
}

.pb-30 {
  padding-bottom: 1.875rem;
}

.pb-40 {
  padding-bottom: 2.5rem;
}

.px-10 {
  padding-left: .625rem;
  padding-right: .625rem;
}

.px-1rem {
  padding-left: 1rem;
  padding-right: 1rem;
}

.px-20 {
  padding-left: 1.25rem;
  padding-right: 1.25rem;
}

.px-30 {
  padding-left: 1.875rem;
  padding-right: 1.875rem;
}

.px-40 {
  padding-left: 2.5rem;
  padding-right: 2.5rem;
}

.px-80 {
  padding-left: 5rem;
  padding-right: 5rem;
}

.px-100 {
  padding-left: 6.25rem;
  padding-right: 6.25rem;
}

.py-6 {
  padding-top: .375rem;
  padding-bottom: .375rem;
}

.py-8 {
  padding-top: .5rem;
  padding-bottom: .5rem;
}

.py-10 {
  padding-top: .625rem;
  padding-bottom: .625rem;
}

.py-1rem {
  padding-top: 1rem;
  padding-bottom: 1rem;
}

.py-20 {
  padding-top: 1.25rem;
  padding-bottom: 1.25rem;
}

.py-30 {
  padding-top: 1.875rem;
  padding-bottom: 1.875rem;
}

.py-40 {
  padding-top: 2.5rem;
  padding-bottom: 2.5rem;
}

.py-50 {
  padding-top: 3.125rem;
  padding-bottom: 3.125rem;
}

.py-60 {
  padding-top: 3.75rem;
  padding-bottom: 3.75rem;
}

.p-5 {
  padding: .3125rem !important;
}

.p-10 {
  padding: .625rem;
}
.p-1rem {
  padding: 1rem;
}
.p-20 {
  padding: 1.25rem;
}

.p-30 {
  padding: 1.875rem;
}

.p-40 {
  padding: 2.5rem;
}
