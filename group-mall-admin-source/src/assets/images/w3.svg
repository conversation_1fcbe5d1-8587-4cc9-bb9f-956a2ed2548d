<?xml version="1.0" encoding="utf-8"?>
<svg version="1.1" xmlns:xlink="http://www.w3.org/1999/xlink" width="275px" height="138px" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <filter x="549px" y="16px" width="275px" height="138px" filterUnits="userSpaceOnUse" id="filter3">
      <feOffset dx="5" dy="5" in="SourceAlpha" result="shadowOffsetInner" />
      <feGaussianBlur stdDeviation="4" in="shadowOffsetInner" result="shadowGaussian" />
      <feComposite in2="shadowGaussian" operator="atop" in="SourceAlpha" result="shadowComposite" />
      <feColorMatrix type="matrix" values="0 0 0 0 0.650980392156863  0 0 0 0 0.603921568627451  0 0 0 0 0.988235294117647  0 0 0 0.376470588235294 0  " in="shadowComposite" />
    </filter>
    <g id="widget4">
      <image preserveAspectRatio="none" style="overflow:visible" width="259" height="122" xlink:href="data:image/png;base64,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" x="552px" y="19px" />
    </g>
  </defs>
  <g transform="matrix(1 0 0 1 -549 -16 )">
    <use xlink:href="#widget4" filter="url(#filter3)" />
    <use xlink:href="#widget4" />
  </g>
</svg>