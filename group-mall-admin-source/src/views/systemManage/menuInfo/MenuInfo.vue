<template>
    <div>
        <div class="button--line">
            <div style="display:flex;">
                <el-button @click="modifyList(3)" type="primary" v-if="isSupper||addButton">新增菜单</el-button>
            </div>
            <!--<div class="button--right">拖拽可调整展示分类顺序</div>-->
        </div>
        <div class="borderBox">
            <div class="drag__list">
                <div v-for="(item, index) in navigationListCom" :key="index">
                    <el-collapse v-model="showList">
                        <el-collapse-item :disabled="item.authMenuInfoSecondVos.length == 0" :name="index">
                            <div class="drag__list--item" slot="title">
                                <div class="borderBox__first">
                                    <div>{{ item.menuName }}</div>
                                    <div class="borderBox__first--deal">
                                        <span style="color:#2D8CF0" @click.stop="modifyList(1, item)"  v-if="isSupper||addButton">新增二级菜单</span>
                                        <span style="color:#2D8CF0" @click.stop="modifyList(2, item)"  v-if="isSupper||editButton">编辑</span>
                                        <span style="color:#FA6465" @click.stop="btnDelClass(item)"  v-if="isSupper||deleteButton">删除</span>
                                    </div>
                                </div>
                            </div>
                            <div class="borderBox__child" v-for="childData in item.authMenuInfoSecondVos">
                                <div class="borderBox__child--left">
                                    {{ childData.menuName }}
                                </div>
                                <div class="borderBox__child--right">
                                    <div style="color:#2D8CF0" @click="buttonList(childData)">
                                        按钮权限
                                    </div>
                                    <div style="color:#2D8CF0" @click="editClassTwo(childData)">
                                        编辑
                                    </div>
                                    <div style="color:#FA6465" @click="delClassTwo(childData)">
                                        删除
                                    </div>
                                </div>
                            </div>
                        </el-collapse-item>
                    </el-collapse>
                </div>
            </div>
            <div class="emptyLine" v-if="hasList">
                暂无数据~
            </div>
        </div>
        <el-dialog :visible.sync="editFlag" width="30%" :before-close="cancel">
            <span slot="title">
                <div class="dialogTitle">{{ addFlag ? "编辑" : "新增" }}菜单</div>
            </span>
            <div class="dialog__radio">
                <span style="margin-right:30px;">*级别：</span>
                <el-radio v-model="rankRadio" label="1" :disabled="levelOne" @change="changeRadio">一级</el-radio>
                <el-radio v-model="rankRadio" label="2" :disabled="levelTwo" @change="changeRadio">二级</el-radio>
            </div>
            <div v-if="rankRadio == '1'">
                <div class="dialog__line">
                    <div class="dialog__line__fir">
                        *菜单名称：
                        <el-input placeholder="请输入菜单名称" v-model="currentItem.menuName" style="width:180px;"></el-input>
                    </div>
                </div>
                <div class="dialog__line">
                    <div class="dialog__line__fir">
                        *菜单标识：
                        <el-input placeholder="请输入菜单标识" v-model="currentItem.menuCode" style="width:180px;"></el-input>
                    </div>
                </div>
            </div>
            <div v-if="rankRadio == '2'">
                <div class="dialog__line">
                    <div class="dialog__line__fir">
                        *一级菜单：
                        <el-select v-model="currentItem.menuName" v-if="rankRadio == '2'" placeholder="请选择一级菜单"
                            @change="select">
                            <el-option v-for="item in navigationListCom" :key="item.menuName" :label="item.menuName"
                                :value="item.menuName"></el-option>
                        </el-select>
                    </div>
                </div>
                <div class="dialog__line">
                    <div class="dialog__line__sec">
                        *二级菜单：
                        <div style="width:400px;border:1px solid #999;">
                            <div class="dialog__line__sec--head">
                                <div>二级菜单名称</div>
                                <div>二级菜单标识</div>
                                <div>操作</div>
                            </div>
                            <div v-for="(child, index) in currentItem.menuVos" :key="index">
                                <div class="dialog__line__sec--body">
                                    <el-input v-model="child.menuName" style="width:150px;"
                                        placeholder="请输入二级菜单名称"></el-input>
                                    <el-input v-model="child.menuCode" style="width:150px;"
                                        placeholder="请输入二级菜单标识"></el-input>
                                    <div v-if="!temObj || !temObj.menuName">
                                        <div class="add--text" @click="getAddClass" style="color:#2D8CF0;cursor:pointer"
                                            v-if="index + 1 === currentItem.menuVos.length">
                                            新增
                                        </div>
                                        <span style="color:#e96900;cursor:pointer;" @click="delAddClass(index)">删除</span>
                                    </div>
                                </div>
                            </div>
                        </div>

                    </div>
                </div>
            </div>
            <span slot="footer" class="dialog--footer">
                <el-button @click="cancel">取 消</el-button>
                <el-button type="primary" @click="editClass()" v-if="temObj && temObj.menuName">确 定</el-button>
                <el-button type="primary" @click="editOk" v-else>确 定</el-button>
            </span>
        </el-dialog>
    </div>
</template>
<script lang="ts">
import { Vue, Component, Watch } from "vue-property-decorator";
import {
    addList,
    getList,
    addSecondList,
    delMenu,
    updateList
} from "@/api/menuInfo/menuInfo";
import { MenuInfoType, MenuInfoChildType } from "../menuInfo/MenuInfoType";
@Component({
    components: {

    },
})
export default class MenuInfo extends Vue {
    /** 展示分类 */
    navigationListCom: Array<MenuInfoType> = [];
    /** 列表中选择一级分类 */
    tamValue = {} as MenuInfoType;

    /** 编辑二级暂存 */
    temObj = null as MenuInfoChildType | null;
    /** 编辑一级二级菜单 */
    rankRadio = "1";
    /** 编辑弹窗 */
    editFlag = false;
    /** 编辑或新增状态 */
    addFlag = false;

    /** 一级单选框 */
    levelOne = false;

    /** 二级单选框 */
    levelTwo = false;
    /** 是否有数据 */
    hasList = false;
    /** 展开数组 */
    showList: number[] = [];
    /** 处理的分类对象 */
    currentItem = {
        id: "",
        menuName: "",
        menuCode: "",
        level: 0,
        menuPid: 0,
        menuVos: [
            {
                id: "",
                level: 1,
                menuName: "",
                menuCode: "",
                menuPid: "",
            },
        ],
    };

    menuName = "菜单管理";
    isSupper = 0;

    addButtonCode = "menuInfo.add";
    addButton = false;

    editButtonCode = "menuInfo.edit";
    editButton = false;

    deleteButtonCode = "menuInfo.delete";
    deleteButton = false;



    mounted() {
        this.getAllList();
        this.buttonAuth();
    }

    buttonAuth() {

        this.isSupper = this.$STORE.userStore.userInfo.isSupper
		let authMenuButtonVos = this.$STORE.userStore.userInfo.authMenuButtonVos.filter(i => i.menuName == this.menuName)

		let buttonList = [];

		authMenuButtonVos.forEach(element => {
			buttonList.push(element.buttonCode);
		});

        var addButtonData = buttonList.find(e => e == this.addButtonCode);
        if (addButtonData != null && addButtonData != undefined) {
			this.addButton = true;
		}

        var deleteButtonData = buttonList.find(e => e == this.deleteButtonCode);
        if (deleteButtonData != null && deleteButtonData != undefined) {
            this.deleteButton = true;
        }
        
        var editButtonData = buttonList.find(e => e == this.editButtonCode);
        if (editButtonData != null && editButtonData != undefined) {
            this.editButton = true;
        }
    }
    getAllList() {
        getList({}).then(res => {
            this.navigationListCom = res.data;
        });
    }
    @Watch("navigationListCom", { deep: true })
    handleNavigationListCom() {
        this.hasList = this.navigationListCom.length === 0 ? true : false;
        this.navigationListCom.forEach((item, index) => {
            if (item.authMenuInfoSecondVos.length > 0) {
                this.showList.push(index);
            }
        });
    }
    /**
     * 新增下级分类
     */
    getAddClass() {
        this.currentItem.menuVos.push({
            id: "",
            level: 1,
            menuName: "",
            menuCode: "",
            menuPid: "",
        });
    }
    /**
 * 删除新增下级分类
 */
    delAddClass(index: number) {
        if (this.currentItem.menuVos.length === 1) {
            this.$message.error("至少保留一行子分类");
        } else {
            this.currentItem.menuVos.splice(index, 1);
        }
    }
    /** 编辑 新增 操作 */
    modifyList(type: number, item: MenuInfoType) {
        this.editFlag = true;
        this.levelOne = false;
        this.levelTwo = false;
        if (type === 3) {
            this.rankRadio = "1";
            this.addFlag = false;
        } else if (type === 2) {
            this.rankRadio = "1";
            this.levelTwo = true;
            this.addFlag = true;
            this.currentItem = JSON.parse(JSON.stringify(item));
        } else if (type === 1) {
            this.rankRadio = "2";
            this.addFlag = false;
            this.rankRadio = "2";
            this.addFlag = false;
            this.currentItem.menuName = item.menuName;
            this.currentItem.menuCode = item.menuCode
            this.currentItem.menuVos = [];
            this.currentItem.menuVos.push({
                id: "",
                level: 1,
                menuName: "",
                menuPid: "",
                menuCode: "",
            });
            this.tamValue = item;
        }
    }
    /**
 * 新增是获取一级分类下数据
 */
    select(e: string) {
        this.navigationListCom.forEach(item => {
            if (item.menuName === e) {
                this.tamValue = item;
            }
        });
    }
    /** 弹窗取消 */
    cancel() {
        this.rankRadio = "1";
        this.editFlag = false;
        this.temObj = null;
        this.levelOne = false;
        this.levelTwo = false;
        if (this.currentItem.menuVos.length > 0) {
            this.currentItem.menuVos[0].menuName = "";
            this.currentItem.menuVos[0].menuCode = "";
        }
        this.currentItem = {
            id: "",
            menuName: "",
            menuCode: "",
            level: 0,
            menuPid: 0,
            menuVos: [
                {
                    id: "",
                    level: 1,
                    menuName: "",
                    menuCode: "",
                    menuPid: "",
                }
            ]
        };
    }
    /**
 * 选择一级或二级分类
 */
    changeRadio() {
        this.currentItem = {
            id: "",
            menuName: "",
            level: 0,
            menuPid: 0,
            menuCode: "",
            menuVos: [
                {
                    id: "",
                    level: 1,
                    menuName: "",
                    menuPid: "",
                    menuCode: "",
                },
            ],
        };
    }
    /**
 * 完成菜单的新增/编辑
 */
    editOk() {
        if (this.rankRadio === "1") {
            this.dealLevelOne();
        } else if (this.rankRadio === "2") {
            this.dealLevelTwo();
        }
    }
    /** 处理一级展示分类 */
    dealLevelOne() {
        if (this.currentItem.menuName === "") {
            this.$message.error("菜单名称不能为空");
            return;
        }
        if (this.currentItem.menuCode === "") {
            this.$message.error("菜单标识不能为空");
            return;
        }
        if (this.currentItem.menuName !== "" && this.currentItem.menuCode !== "") {
            if (this.addFlag === false) {
                const param = {
                    id: this.currentItem.id,
                    menuName: this.currentItem.menuName,
                    level: 0,
                    menuPid: "0",
                    menuCode: this.currentItem.menuCode,
                };
                addList(param)
                    .then(res => {
                        if (res.code === 200) {
                            this.$message.success("添加成功");
                            this.editFlag = false;
                            //this.getAllList(true);
                            this.currentItem.menuName = "";
                            this.currentItem.menuCode = "";
                            this.getAllList();
                        }
                    })
                    .catch(res => {
                        this.$message.error(res);
                    });
            } else {
                this.currentItem.level = 0;
                this.currentItem.menuPid = 0;
                console.log("===========this.currentItem=============", this.currentItem);
                updateList(this.currentItem)
                    .then(res => {
                        if (res.code === 200) {
                            this.$message.success("编辑成功");
                            this.getAllList();
                            this.editFlag = false;
                            this.currentItem.menuCode = "";
                            this.currentItem.menuName = "";
                        }
                    })
                    .catch(res => {
                        this.$message.error(res);
                    });
            }
        }
    }
    /** 处理二级展示分类 */
    dealLevelTwo() {
        this.currentItem.id = this.tamValue.id as string;
        const length = this.currentItem.menuVos.filter(v => {
            return v.menuName === "" || v.menuCode === "";
        }).length;
        if (!this.currentItem.menuName) {
            this.$message.error("请选择一级菜单");
            return;
        }
        if (length > 0) {
            this.$message.error("请输入二级菜单");
            return;
        }
        const arry = this.currentItem.menuVos.map(item => {
            return item.menuName || item.menuCode;
        });

        let chongfu = arry.every((item, index, self) => {
            return self.indexOf(item) === index;
        });
        if (!chongfu) {
            this.$message.error("输入的二级分类重复");
            return;
        }

        this.currentItem.menuVos.forEach(item => {
            item.menuPid = this.currentItem.id;
        });
        addSecondList(this.currentItem.menuVos)
            .then(res => {
                if (res.code === 200) {
                    this.$message.success("添加成功");
                    this.getAllList();
                    this.editFlag = false;
                    this.currentItem.menuName = "";
                    this.currentItem.menuCode = "";
                }
            })
            .catch(res => {
                this.$message.error(res);
            });
    }


    /** 删除分类 */
    btnDelClass(item: MenuInfoType) {
        this.$confirm("确定要删除选中菜单吗?", "提示", {
            confirmButtonText: "确定",
            cancelButtonText: "取消",
            type: "warning",
        }).then(() => {
            delMenu(item.id, {})
                .then(res => {
                    if (res.code === 200) {
                        this.editFlag = false;
                        this.$message.success("删除成功");
                        this.getAllList();
                    }
                })
                .catch(err => {
                    this.$message.error(err);
                });
        });
    }

    /**
   * 删除二级菜单
   */
    delClassTwo(item: MenuInfoChildType) {
        this.$confirm("确定要删除此二级分类吗?", "提示", {
            confirmButtonText: "确定",
            cancelButtonText: "取消",
            type: "warning",
        }).then(() => {
            delMenu(item.id, {})
                .then(res => {
                    if (res.code === 200) {
                        this.$message.success("删除成功");
                        this.getAllList();
                    }
                })
                .catch(err => {
                    this.$message.error(err);
                });
        });
    }

    buttonList(item: MenuInfoChildType) {
        this.$router.push({
            name: "buttonInfo",
            query: { "id":item.id,"name":item.menuName },
        });
    }
    /**
 * 编辑二级分类
 */
    editClassTwo(item: MenuInfoChildType) {
        this.currentItem = {
            id: "",
            menuName: "",
            level: 0,
            menuPid: 0,
            menuCode: "",
            menuVos: [
                {
                    id: "",
                    level: 1,
                    menuName: "",
                    menuPid: "",
                    menuCode: "",
                },
            ],
        };
        this.navigationListCom.forEach(v => {
            v.authMenuInfoSecondVos.forEach(i => {
                if (i.id === item.id) {
                    this.currentItem.menuVos[0].menuName = i.menuName;
                    this.currentItem.menuVos[0].menuCode = i.menuCode;
                    this.currentItem.menuName = v.menuName;
                }
            });
        });
        this.addFlag = true;
        this.editFlag = true;
        this.rankRadio = "2";
        this.levelOne = true;
        this.temObj = item;
    }

    /**
 * 保存二级分类编辑
 */
    editClass() {
        const temObj = this.temObj as MenuInfoChildType;
        const tamValue = this.tamValue;
        this.currentItem.menuVos[0].id = String(temObj.id);
        this.navigationListCom.forEach(v => {
            v.authMenuInfoSecondVos.forEach(i => {
                if (i.id === temObj.id) {
                    this.currentItem.menuVos[0].menuPid = String(v.id);
                }
            });
        });
        // console.log("============this.saleMode ? this.saleMode : ==============",this.saleMode ? this.saleMode : "");
        updateList({
            id: this.currentItem.menuVos[0].id,
            level: 1,
            menuName: this.currentItem.menuVos[0].menuName,
            menuPid: tamValue.id
                ? tamValue.id
                : this.currentItem.menuVos[0].menuPid,
            menuCode: this.currentItem.menuVos[0].menuCode,

        })
            .then(res => {
                if (res.code === 200) {
                    this.$message.success("修改成功");
                    this.editFlag = false;
                    this.currentItem.menuName = "";
                    this.currentItem.menuCode = "";
                    this.getAllList();
                    this.temObj = null;
                }
            })
            .catch(res => {
                this.$message.error(res);
            });
    }
}

</script>
<style lang="scss" scoped>
@import "@/assets/styles/mixins/mixins.scss";

@include b(borderBox) {
    @include e(first) {
        background-color: #f2f2f6;
        // padding: 0px 10px;
        display: flex;
        justify-content: space-between;

        @include m(deal) {
            margin-right: 10px;
            display: flex;
            justify-content: space-between;
            width: 163px;
            cursor: pointer;
        }
    }

    @include e(child) {
        padding: 15px 10px 15px 20px;
        border-bottom: 1px solid #f2f2f2;
        display: flex;
        justify-content: space-between;

        @include m(left) {
            display: flex;
            justify-content: space-between;
            width: 350px;
            padding-left: 10px;
        }

        @include m(right) {
            display: flex;
            justify-content: space-between;
            width: 138px;
            cursor: pointer;
        }
    }

    /deep/ .el-icon-arrow-right:before {
        content: "\e791";
    }

    /deep/ .el-collapse-item__content {
        padding-bottom: 0;
    }

    /deep/ .el-collapse-item__header {
        background-color: #f2f2f6;
        padding-left: 10px;
    }
}

.borderBox__child:hover {
    background-color: #f5f5f5;
    cursor: move;
}

.emptyLine {
    width: 100%;
    height: 80px;
    background-color: white;
    display: flex;
    -webkit-box-align: center;
    -ms-flex-align: center;
    align-items: center;
    -webkit-box-pack: center;
    justify-content: center;
    font-size: 14px;
    color: #b3b3b3;
    border-bottom: 1px solid #ebeef5;
    border-top: 1px solid #ebeef5;
}

@include b(drag) {
    @include e(list) {
        .el-collapse-item__header {
            display: flex;
            flex-direction: row-reverse;
        }

        .el-collapse {
            border-top: 0px;
        }

        @include m(item) {
            width: 100%;
        }

        .is-disabled {
            .el-collapse-item__header {
                cursor: default !important;
                color: #303133 !important;
            }

            .el-icon-arrow-right {
                opacity: 0;
            }
        }
    }
}

.button--line {
    padding-top: 5px;
    padding-bottom: 15px;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.button--right {
    // padding-right: 20px;
    color: #7f7f7f;
    font-size: 13px;
}

.dialogTitle {
    display: flex;
    justify-content: center;
    font-weight: bold;
    font-size: 16px;
}

.dialog__radio {
    display: flex;
    margin-left: 20px;
    margin-bottom: 20px;
}

.dialog__line {
    display: flex;
    padding-left: 20px;
    padding-top: 10px;
    justify-content: flex-start;
    align-items: flex-start;

    &__fir {
        width: 100%;
        text-align: right;
        display: flex;
        align-items: center;
    }

    &__sec {
        width: 100%;
        display: flex;
        margin-top: 10px;

        &--head {
            padding: 10px;
            display: flex;
            justify-content: space-between;
            font-size: 12px;
            background-color: #f6f6f6;
        }

        &--body {
            width: 100%;
            display: flex;
            justify-content: space-between;
            align-items: center;
            border-bottom: 1px solid #999;
            padding: 10px;
        }
    }
}

.dialog--footer {
    display: flex;
    align-items: center;
    justify-content: center;
}
</style>