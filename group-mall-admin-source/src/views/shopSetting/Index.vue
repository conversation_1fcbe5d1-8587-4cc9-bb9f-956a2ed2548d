<template>
    <div class="auth payment">
        <div class="tip">
            <div class="tip__lump"></div>
            <span class="tip__title">模板设置</span>
        </div>
        <el-form ref="configRef" :rules="rules" :model="shopSetting" label-position="left" label-width="180px">
            <el-form-item label="店铺名称" prop="miniModel">
                <el-row :gutter="15">
                    <el-col :span="12">
                        <td>{{ shopsPartner.name }}</td>
                    </el-col>
                </el-row>
            </el-form-item>
            <el-form-item label="店铺模板" prop="miniModel">
                <el-select v-model="shopSetting.platformTemplateId" placeholder="请选择">
                    <el-option v-for="item in options" :key="item.value" :label="item.label" :value="item.value">
                    </el-option>
                </el-select>
            </el-form-item>
            <el-form-item>
                <el-row :gutter="15">
                    <el-col :span="12">
                        <div class="auth__buttons">
                            <el-button type="primary" @click="submit" v-if="isSupper||saveButton">保存
                            </el-button>
                        </div>
                    </el-col>
                </el-row>
            </el-form-item>
        </el-form>
    </div>
</template>

<script lang="ts">

import { Vue, Component } from "vue-property-decorator";
import { getShopsPartner, getPlatformRenovationTemplate, copyByPlatformTemplate } from '@/api/shopsPartner/shopsPartner';
@Component({
    components: {

    },
})
export default class Index extends Vue {


    shopsPartner = {};

    options = [];
    // 店铺设置
    shopSetting = {
        // 平台商家模板id
        platformTemplateId: ""
    };
    rules = {
        platformTemplateId: [{ required: true, message: "平台商家模板id不可为空", trigger: "change" }],
    };
    menuName = "模板设置";
    buttonList = [];
    isSupper = 0;
    saveButtonCode = "shopSetting.save";
    saveButton = false;

    mounted() {
        this.getShopsPartner();
        this.getPlatformRenovationTemplate();
        this.buttonAuth();
    }

    buttonAuth() {
        this.isSupper = this.$STORE.userStore.userInfo.isSupper
        let authMenuButtonVos = this.$STORE.userStore.userInfo.authMenuButtonVos.filter(i => i.menuName == this.menuName)

        let buttonList = [];

        authMenuButtonVos.forEach(element => {
            buttonList.push(element.buttonCode);
        });

        this.buttonList = buttonList

        var saveButtonData = buttonList.find(e => e == this.saveButtonCode);

        if (saveButtonData != null && saveButtonData != undefined) {
            this.saveButton = true;
        }
    }
    getShopsPartner() {
        getShopsPartner({}).then(res => {
            this.shopsPartner = res.data
        })
    }

    getPlatformRenovationTemplate() {
        getPlatformRenovationTemplate({}).then(res => {
            let options = []
            res.data.forEach(i => {
                console.log(i);
                let option = {};
                option.label = i.name
                option.value = i.id
                options.push(option);
            });
            this.options = options
        })
    }
    async submit() {
        if (this.shopSetting.platformTemplateId != null && this.shopSetting.platformTemplateId != "") {
            copyByPlatformTemplate(this.shopSetting).then(res => {
                if (res.code == 200) {
                    this.$message.success("保存成功！");
                }
            }).catch(err => {
                this.$message.error(err);
            })
        } else {
            this.$message.warning("请选择店铺模板");
        }
    }
}
</script>

<style lang="scss" scoped>
/deep/ .tip {
    vertical-align: center;
    background-color: rgba(246, 248, 250, 1);
    padding: 15px 15px 15px 30px;
    margin-bottom: 30px;

    .tip__title {
        margin-left: 12px;
        color: #586884;
        font-weight: 700;
    }

    .tip__lump {
        display: inline-block;
        width: 3px;
        height: 12px;
        background-color: rgba(255, 153, 0, 1);
    }
}
</style>