<template>
	<div>  
	 <!-- 发布通惠证 -->
	<releaseCertificate></releaseCertificate>
	</div>
</template>

<script lang="ts">
import { Vue, Component, Ref } from "vue-property-decorator";
// import { DatePickerOptions } from "element-ui/types/date-picker";
// import DateUtil from "@/store/modules/date";
// import { SearchState, SearchKeyType } from "./components/searchType";

import releaseCertificate from "./components/releaseCertificate/releaseCertificate.vue";
// import { GoodState, ApiSpecArea } from "./marketType";
// import { getAddProductVo, addIntegralActivity, queryIntegralActivity,editIntegralActivity } from "@/api/integralApi/integralApi";

@Component({
  components: {
    releaseCertificate
  }
})
export default class addCertificate  extends Vue {
  //   @Ref()
  //   readonly ListApart?: HTMLFormElement;

  activeName = '发布通惠证'
  //   chooseId: string | number = "108";
  list = [{ modeName: '发布通惠证', id: '' }]
  }
</script>

<style>
</style>