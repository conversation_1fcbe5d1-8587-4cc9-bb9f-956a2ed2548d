<template>
  <div>
    <!--  <el-tabs v-model="activeName" @tab-click="handleClick">
        <el-tab-pane v-for="(item, index) in list" :key="index" :label="item.modeName" :name="item.modeName"
          :status="item.status"></el-tab-pane>
      </el-tabs> -->
    <ListApart :chooseStatus="chooseStatus" ref="ListApart"></ListApart>

  </div>
</template>

<script lang="ts">
import { Vue, Component, Ref } from "vue-property-decorator";
import ListApart from "./ListApart.vue";
import { GoodState, ApiSpecArea } from "./marketType";
@Component({
  components: {
    ListApart
  }
})
export default class Goods extends Vue {
  @Ref()
  readonly ListApart?: HTMLFormElement;
  // 状态:100->待审核;101->审核通过;200->驳回;300->终止
  list: any = [{ modeName: '待审核', status: '100' },
  { modeName: '已审核', status: '101' },
  { modeName: '驳回', status: '200' },
  { modeName: '终止', status: '300' },
  { modeName: '全部', status: '' }
  ]
  activeName = '全部';
  chooseStatus: string | number = "";
  /**
   * 顶部专区选择
   */
  handleClick(tab: { index: number }) {
    this.chooseStatus = this.list[tab.index].status || "";
  }

}

</script>

<style></style>