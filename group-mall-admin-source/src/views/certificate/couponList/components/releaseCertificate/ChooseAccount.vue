<template>
    <el-dialog :visible.sync="dialogVisible" highlight-current-row width="80%" :before-close="handleClose">
        <el-form ref="form" :model="searchType" label-width="120px">
            <el-row>
                <el-col :span="8">
                    <el-form-item label="客户昵称">
                        <el-input v-model="searchType.nikeName" placeholder="请输入客户昵称" style="width: 200px;"></el-input>
                    </el-form-item>
                </el-col>
                <el-col :span="8">
                    <el-form-item label="客户电话号码">
                        <el-input v-model="searchType.phone" placeholder="请输入客户电话号码" style="width: 200px;"></el-input>
                    </el-form-item>
                </el-col>
                <el-col :span="8">
                    <el-form-item label="客户会员等级">
                        <el-input v-model="searchType.memberLevel" placeholder="请输入客户会员等级"
                            style="width: 200px;"></el-input>
                    </el-form-item>
                </el-col>
                <el-col :span="8">
                    <el-form-item label="标签名称">
                        <el-input v-model="searchType.tagName" placeholder="请输入标签名称" style="width: 200px;"></el-input>
                    </el-form-item>
                </el-col>
                <el-col :span="8">
                    <el-button @click="searchAccount" type="primary" size="mini" round
                        style="margin-top: 2px;">搜索</el-button>
                </el-col>
            </el-row>
        </el-form>
        <el-table ref="multipleTable" :data="accountList" tooltip-effect="dark" style="width: 100%" :row-key="getRowKeys"
            @selection-change="handleSelectionChange">
            <el-table-column type="selection" :reserve-selection="true">
            </el-table-column>
            <el-table-column label="序号" type="index">
            </el-table-column>
            <el-table-column width="20">
            </el-table-column>
            <el-table-column prop="nikeName" label="客户昵称" show-overflow-tooltip>
            </el-table-column>
            <el-table-column prop="phone" label="客户电话号码" show-overflow-tooltip>
            </el-table-column>
            <el-table-column prop="memberLevel" label="客户会员等级" show-overflow-tooltip>
            </el-table-column>
            <el-table-column prop="tagName" label="标签名称" show-overflow-tooltip>
            </el-table-column>
        </el-table>
        <PageManage :pageSize="size" :pageNum="current" :total="total" @handleSizeChange="handleSizeChange"
            @handleCurrentChange="handleCurrentChange" />
        <span slot="footer" class="dialog-footer">
            <el-button @click="close">取 消</el-button>
            <el-button type="primary" @click="primaryButton">确 定</el-button>
        </span>
    </el-dialog>
</template>
<script lang="ts">
import { Vue, Component, Prop } from "vue-property-decorator";
import PageManage from "@/components/PageManage.vue";
import {
    getChooseAccount
} from "@/api/customer/customer";
@Component({
    components: {
        PageManage,
    }
})
export default class ChooseAccount extends Vue {
    dialogVisible = false;
    selectedArray = []
    searchType = {
        nikeName: "",
        phone: "",
        memberLevel: "",
        tagName: "",
    }
    current = 1
    size = 10
    total = 0
    account = []
    accountList = []
    mounted() {

    }
    handleClose(done) {
        this.selectedArray = [];
        this.$refs.multipleTable.clearSelection();
        done();
    }
    searchAccount() {
        this.current = 1;
        this.size = 10;
        this.getAccountList();
    }
    getAccountList() {
        let params = this.searchType
        params.current = this.current
        params.size = this.size
        let dataList = this.account;
        let accountIds = [];
        if (dataList != null && dataList.length > 0) {
            dataList.forEach(element => {
                accountIds.push(element.sourceId);
            });
        }
        params.accountIds = accountIds
        getChooseAccount(params).then((res) => {
            this.accountList = res.data.list
            this.total = res.data.total
            this.dialogVisible = true
        }).catch((err) => {
            this.$message.error(err)
        })
    }
    getRowKeys(row) {
        return row.userId //唯一性
    }
    handleSelectionChange(val) {
        this.selectedArray = val
    }
    handleSizeChange(val) {
        this.size = val;
        this.getAccountList()
    }
    handleCurrentChange(val) {
        this.current = val;
        this.getAccountList()
    }
    close() {
        this.selectedArray = []
        this.dialogVisible = false;
        this.$refs.multipleTable.clearSelection();
    }
    primaryButton() {
        this.dialogVisible = false;
        this.account = this.account.concat(this.selectedArray)
        console.log("this.account", this.account);
        this.$emit("handleAccount", this.account)
        this.selectedArray = [];
        this.$refs.multipleTable.clearSelection();
    }
}
</script>
<style lang="scss"></style>