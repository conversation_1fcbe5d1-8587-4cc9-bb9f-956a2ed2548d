<template>
    <el-dialog :visible.sync="dialogVisible" highlight-current-row width="80%" :before-close="handleClose">
        <el-form ref="form" :model="searchType" label-width="90px">
            <el-row>
                <el-col :span="6">
                    <el-form-item label="商品名称">
                        <el-input v-model="searchType.productName" placeholder="请输入商品名称"
                            style="width: 200px;"></el-input>
                    </el-form-item>
                </el-col>
                <el-col :span="6">
                    <el-form-item label="展示分类">
                        <el-select v-model="searchType.showCategoryId" style="width: 200px" placeholder="请选择分类"
                            :popper-append-to-body="false">
                            <el-option label="全部" :value="''" />
                            <el-option-group v-for="group in temAllShowList" :key="group.showCategoryId"
                                :label="group.name">
                                <el-option v-for="item in group.showCategoryVos" :key="item.showCategoryId"
                                    :label="item.name" :value="item.showCategoryId"></el-option>
                            </el-option-group>
                        </el-select>
                    </el-form-item>
                </el-col>
                <el-col :span="6">
                    <el-form-item label="商品状态">
                        <el-select v-model="searchType.status" placeholder="请选择状态" style="width: 150px" clearable>
                            <el-option label="全部" :value="''" />
                            <el-option v-for="tag in statusList" :key="tag.value" :label="tag.key" :value="tag.value" />
                        </el-select>
                    </el-form-item>
                </el-col>
                <el-col :span="6">
                    <el-button @click="searchGoods" type="primary" size="mini" round
                        style="margin-top: 2px;">搜索</el-button>
                </el-col>
            </el-row>
        </el-form>
        <el-table ref="multipleTable" :data="goodList" tooltip-effect="dark" style="width: 100%" :row-key="getRowKeys"
            @selection-change="handleSelectionChange">
            <el-table-column type="selection" :reserve-selection="true">
            </el-table-column>
            <el-table-column label="序号" type="index">
            </el-table-column>
            <el-table-column width="20">
            </el-table-column>
            <el-table-column prop="productName" label="商品名称" show-overflow-tooltip>
            </el-table-column>
            <el-table-column prop="specs" label="商品规格" show-overflow-tooltip>
            </el-table-column>
            <el-table-column prop="categoryName" label="商品分类" show-overflow-tooltip>
            </el-table-column>
            <el-table-column prop="price" label="实售价" show-overflow-tooltip>
            </el-table-column>
            <el-table-column prop="status" label="商品状态" show-overflow-tooltip>
                <template slot-scope="scope">
                    <span v-if="scope.row.status == '0'">下架</span>
                    <span v-if="scope.row.status == '1'">上架</span>
                </template>
            </el-table-column>
        </el-table>
        <PageManage :pageSize="size" :pageNum="current" :total="total" @handleSizeChange="handleSizeChange"
            @handleCurrentChange="handleCurrentChange" />
        <span slot="footer" class="dialog-footer">
            <el-button @click="close">取 消</el-button>
            <el-button type="primary" @click="primaryButton">确 定</el-button>
        </span>
    </el-dialog>
</template>

<script lang="ts">
import { Vue, Component, Prop } from "vue-property-decorator";
import PageManage from "@/components/PageManage.vue";
import {
    getAddProductPackageVo,
    queryShowCategoryListAll
} from "@/api/good/goods";
@Component({
    components: {
        PageManage,
    }
})
export default class ChooseGoods extends Vue {
    dialogVisible = false;
    selectedArray = [];
    temAllShowList = [];
    searchType = {
        productName: "",
        showCategoryId: "",
        status: "",
    }
    statusList = [
        { key: '上架', value: '1' },
        { key: '下架', value: '0' },
    ]
    current = 1
    size = 10
    total = 0
    products = []
    goodList = []
    mounted() {
        this.getAllCategoryList();

    }
    async getAllCategoryList() {
        const param = {

        };
        const { data } = await queryShowCategoryListAll(param);
        this.temAllShowList = JSON.parse(JSON.stringify(data)) || [];
    }
    handleClose(done) {
        this.selectedArray = [];
        this.$refs.multipleTable.clearSelection();
        done();
    }
    searchGoods() {
        this.current = 1;
        this.size = 10;
        this.getGoodsList();
    }
    getGoodsList() {
        let params = this.searchType
        params.current = this.current
        params.size = this.size
        let dataList = this.products;
        let skuIds = [];
        if (dataList != null && dataList.length > 0) {
            dataList.forEach(element => {
                skuIds.push(element.skuId);
            });
        }
        params.skuIds = skuIds
        getAddProductPackageVo(params).then((res) => {
            this.goodList = res.data.list
            this.total = res.data.total
            this.dialogVisible = true
        }).catch((err) => {
            this.$message.error(err)
        })
    }
    getRowKeys(row) {
        return row.productId + '-' + row.skuId //唯一性
    }
    handleSelectionChange(val) {
        this.selectedArray = val
    }
    handleSizeChange(val) {
        this.size = val;
        this.getGoodsList()
    }
    handleCurrentChange(val) {
        this.current = val;
        this.getGoodsList()
    }
    close() {
        this.selectedArray = []
        this.dialogVisible = false;
        this.$refs.multipleTable.clearSelection();
    }
    primaryButton() {
        this.dialogVisible = false;
        this.products = this.products.concat(this.selectedArray)
        console.log("this.products",this.products);
        this.$emit("handleGoods",this.products)
        this.selectedArray = [];
        this.$refs.multipleTable.clearSelection();
    }
}
</script>
<style lang="scss"></style>