<!--
 * @description: 抽离开源版本
 * @Author: chuyinlong
 * @Date: 2021-08-20 17:29:10
 * @LastEditors: chuyinlong
 * @LastEditTime: 2021-08-25 13:52:07
-->
<template>
	<m-card class="form" :needToggle="true">
		<el-form ref="form" :model="searchType" label-width="90px">
			<el-row>
				<el-col :span="12">
					<el-form-item label="通惠证名称">
						<el-input v-model="searchType.ticketName" placeholder="请输入通惠证名称"></el-input>
					</el-form-item>
				</el-col>
				<el-col :span="12">
					<el-form-item label="使用次数">
						<el-input v-model="searchType.useableTimes" placeholder="请输入使用次数"></el-input>
					</el-form-item>
				</el-col>

			</el-row>
			<el-row>

				<el-col :span="12">
					<el-form-item label="开始时间">
						<el-date-picker :picker-options="pickerOptions" v-model="value0" type="datetimerange" range-separator="-" start-placeholder="开始日时间"
							end-placeholder="结束时间" style="width:350px;" @change="chooseTime">
						</el-date-picker>
					</el-form-item>
				</el-col>
				<el-col :span="12">
					<el-form-item label="结束时间">
						<el-date-picker v-model="value1" type="datetimerange" range-separator="-" start-placeholder="开始日时间"
							end-placeholder="结束时间" :picker-options="pickerOptions" style="width:350px;" @change="chooseTimes">
						</el-date-picker>
					</el-form-item>
				</el-col>
			</el-row>
			<el-row>

				<el-col :span="12">
					<el-form-item label="审核状态">
						<el-select v-model="searchType.approvedStatus" placeholder="请选择">
							<el-option v-for="item in options" :key="item.value" :label="item.label"
								:value="item.value">
							</el-option>
						</el-select>
					</el-form-item>
				</el-col>
			</el-row>
			<el-form-item>
				<el-button type="primary" @click="search">搜索</el-button>
			</el-form-item>
		</el-form>
	</m-card>
	<!-- </div> -->
</template>

<script lang="ts">
	import { Vue, Component, Watch, Prop } from "vue-property-decorator";
	import { SearchState, SearchKeyType } from "./searchType";
	import DateUtil from "@/store/modules/date";
	import { DatePickerOptions } from "element-ui/types/date-picker";
	// import { watch } from "vue";

	@Component
	export default class Searchs extends Vue implements SearchState {
		name = "Searchs";
		@Prop({})
		status! : string;

		pickerOptions : DatePickerOptions = {
			shortcuts: [{
			            text: '最近一周',
			            onClick(picker) {
			              const end = new Date();
			              const start = new Date();
			              start.setTime(start.getTime() - 3600 * 1000 * 24 * 7);
			              picker.$emit('pick', [start, end]);
			            }
			          }, {
			            text: '最近一个月',
			            onClick(picker) {
			              const end = new Date();
			              const start = new Date();
			              start.setTime(start.getTime() - 3600 * 1000 * 24 * 30);
			              picker.$emit('pick', [start, end]);
			            }
			          }, {
			            text: '最近三个月',
			            onClick(picker) {
			              const end = new Date();
			              const start = new Date();
			              start.setTime(start.getTime() - 3600 * 1000 * 24 * 90);
			              picker.$emit('pick', [start, end]);
			            }
			          }]
		};
		// 审核状态:100->待审核;101->审核通过;200->驳回
		options = [{
			value: '',
			label: '全部'
		}, {
			value: 100,
			label: '未审核'
		}, {
			value: 101,
			label: '已审核'
		}];
		showCateList = [];

		searchType = {
			useableTimes: '',
			ticketName: '',
			approvedStatus: '',
			startTimeBegin:'',
			startTimeEnd:'',
			endTimeEnd:'',
			endTimeBegin:''
		} as SearchKeyType;
		value0 = ''
		value1 = ''

created(){
	var cache = JSON.parse(
		localStorage.getItem("cache_certificateList_search_form") || "{}"
	);
	this.searchType = Object.assign(this.searchType, cache) as SearchKeyType;
	this.value0=[this.searchType.startTimeBegin,this.searchType.startTimeEnd];
	this.value1=[this.searchType.endTimeBegin,this.searchType.endTimeEnd];
	// console.log('ffffffffff',this.searchType,cache);
}
		chooseTime(data : any) {
			this.searchType.startTimeBegin = data ? this.dateConversion(data[0]) : "";
			this.searchType.startTimeEnd = data ? this.dateConversion(data[1]) : "";
		}
		chooseTimes(data : any) {
			this.searchType.endTimeBegin = data ? this.dateConversion(data[0]) : "";
			this.searchType.endTimeEnd = data ? this.dateConversion(data[1]) : "";
			console.log('时间', this.searchType);

		}

		dateConversion(value : Date) {
			const date = new DateUtil("").getYMDHMSs(value);
			return date;
		}

		search() {
			// console.log('eeeeeee', this.searchType);
			this.$emit("searchBy", this.searchType);
		}
	}
</script>

<style lang="scss" scoped>
	.el-form-item .el-input {
		width: 224px;
	}

	.el-form-item .el-button {
		width: 90px;
	}

	@include b(form) {
		transform-origin: left top;
		overflow: hidden;
		position: relative;
		transition: all 0.3s ease 0s;

		&.show {
			height: 240px;
			margin-bottom: 20px;
		}

		&.hide {
			margin-bottom: 20px;
			height: 50px;

			.form__btn {
				width: 940px;
				height: 50px;
				background: #f9f9f9;
				line-height: 50px;
				// margin-top: 20px
			}
		}

		@include e(btn) {
			width: 100%;
			position: absolute;
			bottom: 0;
			text-align: center;
			padding-bottom: 20px;

			span {
				cursor: pointer;
			}
		}
	}

	.page {
		// height: 270px;
		background-color: #f9f9f9;
		margin-bottom: 20px;
	}

	@include b(search) {
		display: flex;
		flex-wrap: wrap;

		@include e(item) {
			padding: 20px 40px 10px 40px;
			display: flex;
			justify-content: space-between;
			align-items: center;

			@include m(text) {
				width: 60px;
			}
		}

		@include e(icon) {
			width: 40px;
			text-align: center;
			border-left: 1px solid #dcdfe6;
			cursor: pointer;
			vertical-align: middle;
		}
	}

	@include b(searchButton) {
		margin: 20px 30px;
	}
</style>
