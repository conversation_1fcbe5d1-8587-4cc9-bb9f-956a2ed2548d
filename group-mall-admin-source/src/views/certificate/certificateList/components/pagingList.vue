<template>
	<div style="margin-top: 20px">
		<m-table :data.sync="ticketList" :selection="true" :checked-item.sync="tableCheckedItem" slot="content"
			class="imgView">
			<m-table-column prop="ticketName" label="通惠证名称" :showsSlection="true" width="220">
				<template v-slot="{ row }">
					<div class="goodList">
						{{ row.ticketName }}
					</div>
				</template>
			</m-table-column>
			<m-table-column prop="userName" label="生效时间" width="90">
				<template v-slot="{ row }">
					<span>{{ row.startTime }}</span>
				</template>
			</m-table-column>
			<m-table-column prop="startTime" label="过期时间" width="90">
				<template v-slot="{ row }">
					<span>{{ row.endTime }}</span>
				</template>
			</m-table-column>
			<m-table-column prop="useableTimes" label="使用次数" width="80">
				<template v-slot="{ row }">
					<span>{{ row.useableTimes==null?'':row.useableTimes }}</span>
				</template>
			</m-table-column>
			<m-table-column prop="useableTimes" label="优惠内容" width="140">
				<template v-slot="{ row }">
					<span v-if="row.ticketType==100">满{{row.fullAmount}}减{{row.promotion}}</span>
					<span v-if="row.ticketType==101">折扣{{row.promotion}}</span>
				</template>
			</m-table-column>
			<m-table-column prop="price" label="售价" width="80">
				<template v-slot="{ row }">
					<span>{{ row.price==null?'':row.price }}</span>
				</template>
			</m-table-column>
			<m-table-column prop="approvedStatus" label="审核">
				<template v-slot="{ row }">
					<!-- 审核状态:100->待审核;101->审核通过;200->驳回 -->
					<span v-if="row.approvedStatus==100" style="color: #ff0000 ;">未审核</span>
					<span v-else-if="row.approvedStatus==101" style="color: #23c910 ;">已审核</span>
					<span v-else-if="row.approvedStatus==200" style="color: #e80bdc ;">驳回</span>
					<span v-else-if="row.approvedStatus==300" style="color: #e80bdc ;">已停用</span>
				</template>
			</m-table-column>
			<m-table-column prop="projectStatus" label="状态">
				<template v-slot="{ row }">
					<!-- 状态:100->未生效;101->已生效;104->已过期;200->驳回;300->终止 -->
					<span v-if="row.status==100">未生效</span>
					<span v-else-if="row.status==101">已生效</span>
					<span v-else-if="row.status==104">已过期</span>
					<span v-else-if="row.status==200">驳回</span>
					<span v-else-if="row.status==300">已停用</span>
				</template>
			</m-table-column>
			<m-table-column prop="userName" label="操作">
				<template v-slot="{ row }">
					<div class="center">
						<!-- <el-button type="primary" size="mini" round>...</el-button> -->
						<set-drop
		          :dropdownList="itemDropList(row)"
		          @command="getDropdown($event, row)"
		        />

					</div>
				</template>
			</m-table-column>
		</m-table>
		<!-- 审核  是否通过 -->
		<el-dialog :visible.sync="passThrough" width="30%" :before-close="throughClose">
		    <div class="dialogPass">
		        <div>
		            审核通过，通惠证将不可修改，是否操作？
		        </div>
		        <el-button type="success" @click="primaryThrough">确 定</el-button>
		    </div>

		</el-dialog>
	</div>
</template>

<script lang="ts">
	import { Vue, Component, Watch, Prop } from "vue-property-decorator";
	import { SearchKeyType } from "./searchType";
	import SetDrop from "./goodsComp/SetDrop.vue";
	import { ApiSkuType, GoodDetailInfo } from "../goodType";
	import { pagePassTicket,auditIdShops,deleteByIdShops,stopIdShops } from "@/api/certificateApi/certificateApi"
	// import SetDrop from "@/views/customer/common/SetDrop.vue";
	@Component({
		components: {
			SetDrop
		}
	})
	export default class pagingList extends Vue {
		@Prop({})
		changeId! : string;

		@Watch("changeId")
		getSaleMode() {
			this.searchType.status = this.changeId;
			this.getPageTicket();
		}
		searchType = {
			current: 1,
			size: 10
		} as SearchKeyType;
		ticketList = []
		total = 0;
		ids:number=null
		tableCheckedItem = [];
		passThrough: boolean = false

		menuName = "通惠证";

		buttonList = [];

		isSupper = 0;

		deleteButtonCode = "certificateList.delete";

		deleteButton = false;

		editButtonCode = "certificateList.edit";

		editButton = false;

		toExamineButtonCode = "certificateList.toExamine";

		toExamineButton = false;

		stopButtonCode = "certificateList.stop";

		stopButton = false;

		get itemDropList() {
		  return (row: GoodDetailInfo) => {
		    return [
		    {
		        text: "编辑",
		        command: "edit",
		        show:this.isSupper||this.editButton,
		        disabled: false
		      },
		      {
		        text: "审核",
		        command: "examine",
		        show: this.isSupper||this.toExamineButton,
		        disabled: false
		      },
          {
            text: "停用",
            command: "deactivate",
            show: this.isSupper||this.stopButton,
            disabled: false
          },
          {
            text: "删除",
            command: "delete",
            show:row.approvedStatus==100&&(this.isSupper||this.deleteButton),
            disabled: false
          }
		    ];
		  };
		}
		mounted() {
			this.searchType.status = this.changeId;
			// console.log("进入到goodlist",);
					//  加载搜索缓存
			var cache = JSON.parse(
				localStorage.getItem("cache_certificate_search_form") || "{}"
			);
			console.log('获取商品列表查询参数11', this.searchType, cache);
			this.searchType = Object.assign(this.searchType, cache) as SearchKeyType;

			this.getPageTicket();

			this.buttonAuth();

		}

		buttonAuth() {
			this.isSupper = this.$STORE.userStore.userInfo.isSupper
			let authMenuButtonVos = this.$STORE.userStore.userInfo.authMenuButtonVos.filter(i => i.menuName == this.menuName)

			let buttonList = [];

			authMenuButtonVos.forEach(element => {
				buttonList.push(element.buttonCode);
			});

			this.buttonList = buttonList

			var deleteButtonData = buttonList.find(e => e == this.deleteButtonCode);

			if (deleteButtonData != null && deleteButtonData != undefined) {
				this.deleteButton = true;
			}

			var editButtonData = buttonList.find(e => e == this.editButtonCode);

			if (editButtonData != null && editButtonData != undefined) {
				this.editButton = true;
			}

			
			var toExamineButtonData = buttonList.find(e => e == this.toExamineButtonCode);
			console.log(this.toExamineButtonCode);
			if (toExamineButtonData != null && toExamineButtonData != undefined) {
				this.toExamineButton = true;
			}

			var stopButtonData = buttonList.find(e => e == this.stopButtonCode);

			if (stopButtonData != null && stopButtonData != undefined) {
				this.stopButton = true;
			}
		}

		getPageTicket() {
			// 删除请求链接里面的空值
			for (const key in this.searchType) {
			     if (!this.searchType[key]) {
			         this.$delete(this.searchType, key)
			     }
			 }
			pagePassTicket(this.searchType).then((res) => {
				this.ticketList = res.data.list;
				this.total = res.data.total;
				this.$emit("getShowProList", this.ticketList);
			}).catch((err) => {
				this.$message.error(err)
			})
		}
		/**
		 * 获取下拉框
		 */
		getDropdown(val: string | number, row: any) {

		    if(val=='edit'){
		       this.getGoedit(row.id)
		    }
		    if(val=='examine'){
		      // this.getDeactivateIntegral(row.id)
			  this.passThrough = true
			  this.ids=row.id as number
			  // this.approvalType.id = row.id
		    }
			if(val=='delete'){
			   this.deleteByIdShops(row.id)
			}
			if(val=='deactivate'){
			   this.stopIdShops(row.id)
			}


		}
		// 编辑通惠证
		getGoedit(id:string){
			// this.$router.push({query:})
			this.$router.push({
			  name: "AddCertificate",
			  params: { id:String(id)},
			  query: { id:id }
			});
		}
		/**取消审核判断 */
		throughClose() {
		    this.passThrough = false
		}
		/**确定审核通过 */
		primaryThrough() {
		    this.passThrough = false
			console.log('eeeeee',this.ids);
			// String(this.ids)
			// Number(this.ids)
			// (this.ids)
			let aa={'id':Number(this.ids)}
			// let a=JSON.toString(aa)
			// let a=JSON.parse(aa)
			auditIdShops(this.ids).then((res)=>{
				this.$message.success('审核成功')
				this.getPageTicket();
			}).catch((err)=>{
				this.$message.success(err)
			})
		}
		stopIdShops(id:number){
			this.$alert('确定停用该通惠证', '标题名称', {
			         confirmButtonText: '确定',
			         callback: action => {
						console.log('获取下拉框',action);
						if(action=="confirm"){
						stopIdShops(id).then((res)=>{
			           	this.$message.success('停用成功')
						this.getPageTicket();
			           }).catch((err)=>{
			           	this.$message.success(err)
			           })

						}else{
							this.$message('取消停用')
						}

			        
			         }
			       });

		}
		deleteByIdShops(id:number){
			 this.$alert('确定删除该通惠证', '标题名称', {
			          confirmButtonText: '确定',
			          callback: action => {
						if(action=="confirm"){
			            deleteByIdShops(id).then((res)=>{
			            	this.$message.success('删除成功')
							this.getPageTicket();
			            }).catch((err)=>{
			            	this.$message.success(err)
			            })							
						}else{
							this.$message('取消删除')
						}
			          }
			        });

		}

	}
</script>

<style lang="scss" scoped>
  .dialogPass {
      color: #010101;
      display: flex;
      justify-self: center;
      align-items: center;
      flex-direction: column;

      .el-button {
          margin-top: 20px;
          background: rgb(9, 240, 22);
          color: #ffffff;
          width: 102px;
          height: 29px;
          font-size: 18px;
          outline: none;
          border: none;
          text-align: center;
          line-height: 14px;
      }
  }
  .mouseEnter {
    // background-color: red;
    border: 1px solid #ecf6ff;
  }

  .mouseEnter:hover {
    // background-color: green;
    border: 1px solid #d7e0e8;
  }

  .pop--button {
    display: flex;
    justify-content: flex-end;
    margin-right: 10px;
  }

  .goodList {
    width: 200px;
    display: flex;
    justify-content: center;
    text-align: center;
    padding-right: 20px;
    overflow: hidden;




  }

  .upDown {
    display: flex;
    align-items: center;
    justify-content: center;

    &__goodUp {
      display: flex;
      width: 50px;
      height: 20px;
      justify-content: center;
      align-items: center;
      border-radius: 4px;
      color: white;
      margin-right: 10px;
    }

    &__goodDown {
      margin-left: 10px;
      color: #2d8cf0;
      cursor: pointer;
    }
  }

  .commandClass {
    height: 150px;
    overflow: overlay;
  }

  .commandClass::-webkit-scrollbar {
    width: 4px;
    height: 4px;
  }

  .commandClass::-webkit-scrollbar-thumb {
    border-radius: 10px;
    -webkit-box-shadow: inset 0 0 5px rgba(0, 0, 0, 0.2);
    background: rgba(0, 0, 0, 0);
  }

  .commandClass::-webkit-scrollbar-track {
    -webkit-box-shadow: inset 0 0 5px rgba(0, 0, 0, 0.2);
    border-radius: 0;
    background: rgba(0, 0, 0, 0);
  }

  .center {
    display: flex;
    justify-content: center;
	font-size: 30px;
	font-weight: 700;
  }

  .digTitle {
    font-size: 17px;
    font-weight: bold;
  }
  </style>
