<template>
  <div>
    <div class="user-header">
      <el-row>
        <el-col :span="4">
          <el-input placeholder="请输入用户名称" v-model="userName"></el-input>
        </el-col>
        <el-col :span="4">
          <div class="user-btn">
            <el-button type="primary"> 查询</el-button>
            <el-button type="success" icon="el-icon-refresh" circle></el-button>
          </div>

        </el-col>
      </el-row>
    </div>
    <div class="user-content">
      <el-table
          :data="tableData"
          stripe
          style="width: 100%"
          border

      >
        <el-table-column
            prop="date"
            label="日期"
            width="180"
            align="center"
        >
        </el-table-column>
        <el-table-column
            prop="name"
            label="姓名"
            width="180" align="center">
        </el-table-column>
        <el-table-column
            prop="address"
            label="地址" align="center">
        </el-table-column>
        <el-table-column label="" align="center">
          <template slot-scope="scope">
            <el-button type="text" @click="edit()"> 修改</el-button>
          </template>
        </el-table-column>
      </el-table>
    </div>
    <div class="user-footer">
      <el-pagination
          small
          layout="prev, pager, next,sizes"
          :total="50">

      </el-pagination>
    </div>
    <el-dialog :visible.sync="visible" title="修改" width="30%">
      <div class="dialog-content">
        <div>
          <el-row>
            <el-col :span="8">
              <el-input placeholder="修改姓名"></el-input>
            </el-col>
          </el-row>
        </div>

        <div class="tree">
          <el-tree :data="data" show-checkbox :props="defaultProps">

          </el-tree>
        </div>
      </div>

      <span slot="footer" class="dialog-footer">
    <el-button @click="visible = false">取 消</el-button>
    <el-button type="primary" @click="visible = false">确 定</el-button>
  </span>
    </el-dialog>
  </div>
</template>

<script>
export default {
  name: "index",
  data() {
    return {
      data: [{
        label: "一级 1",
        children: [{
          label: "二级 1-1",
          children: [{
            label: "三级 1-1-1",
          }],
        }],
      }, {
        label: "一级 2",
        children: [{
          label: "二级 2-1",
          children: [{
            label: "三级 2-1-1",
          }],
        }, {
          label: "二级 2-2",
          children: [{
            label: "三级 2-2-1",
          }],
        }],
      }, {
        label: "一级 3",
        children: [{
          label: "二级 3-1",
          children: [{
            label: "三级 3-1-1",
          }],
        }, {
          label: "二级 3-2",
          children: [{
            label: "三级 3-2-1",
          }],
        }],
      }],
      defaultProps: {
        children: "children",
        label: "label",
      },
      userName: "",
      visible: false,
      tableData: [{
        date: "2016-05-02",
        name: "王小虎",
        address: "上海市普陀区金沙江路 1518 弄",
      }, {
        date: "2016-05-04",
        name: "王小虎",
        address: "上海市普陀区金沙江路 1517 弄",
      }, {
        date: "2016-05-01",
        name: "王小虎",
        address: "上海市普陀区金沙江路 1519 弄",
      }, {
        date: "2016-05-03",
        name: "王小虎",
        address: "上海市普陀区金沙江路 1516 弄",
      }],
    };
  },
  methods: {
    edit() {
      this.visible = true;

    },
  },
};
</script>

<style lang="scss" scoped>
.user-btn {
  margin-left: 12px;
}

.user-content {
  margin-top: 25px;
}

.user-footer {
  display: flex;
  justify-content: flex-end;
  margin-top: 30px;
}

.tree {
  margin: 15px 0 0 15px;
}


</style>
