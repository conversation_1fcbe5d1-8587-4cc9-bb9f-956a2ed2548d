<template>
    <el-dialog @close="closeDialog" title="" :visible.sync="dialogTableVisible">
        <el-form :model="WareHouseForm">
            <el-row :gutter="24">
                <el-col :span="6">
                    <div class="grid-content bg-purple">
                        <el-form-item label="职员编号" :label-width="formLabelWidth">
                            <el-input v-model="WareHouseForm.empNumber" autocomplete="off" width="100%"></el-input>
                        </el-form-item>
                    </div>
                </el-col>
                <el-col :span="6">
                    <div class="grid-content bg-purple">
                        <el-form-item label="部门名称" :label-width="formLabelWidth">
                            <el-input v-model="WareHouseForm.departmentName" autocomplete="off"></el-input>
                        </el-form-item>
                    </div>
                </el-col>
                <el-col :span="6">
                    <div class="grid-content bg-purple">
                        <el-form-item label="姓名" :label-width="formLabelWidth">
                            <el-input v-model="WareHouseForm.empFullName" autocomplete="off"></el-input>
                        </el-form-item>
                    </div>
                </el-col>
              
                <el-col :span="6" >
                    <el-button type="primary" @click="searchWareHouse(1)">搜索</el-button>
                </el-col>

            </el-row>


        </el-form>

        <el-table :row-key="getRowKeys" ref="multipleTable" tooltip-effect="dark" :data="WareHouseData"
            @selection-change="handleSelectionChange" @row-click="handleClick">
            <el-table-column label width="35">
                <template slot-scope="scope">
                    <el-radio :label="scope.row.outId" v-model="employeeId"
                        @click.native="handleRadioClick(scope.row)">&nbsp;</el-radio>
                </template>
            </el-table-column>
            <el-table-column property="empNumber" label="职员编号" width="150"></el-table-column>
            <el-table-column property="departmentName" label="部门名称"></el-table-column>
            <el-table-column property="empFullName" label="姓名" width="200"></el-table-column>
           
        </el-table>
        <el-button style="margin-top: 20px;" type="primary" @click="handleBind">绑定</el-button>
        <PageManage :pageSize="pageSize" :pageNum="pageNum" :total="total" @handleSizeChange="handleSizeChange"
            @handleCurrentChange="handleCurrentChange" class="PageManage" />
    </el-dialog>
</template>

<script>

import { bindDepartment, pageEmployee } from "@/api/department/department";
import PageManage from '@/components/PageManage.vue';
export default {
    //点击编辑时弹出的仓库弹窗
    name: 'ware',//绑定经手人
    data() {
        return {
            dialogTableVisible: true,
            WareHouseForm: {
                empNumber: '',//职员编号
                departmentName: '',//部门
                empFullName: '',//姓名
                warehouseFullName: '',//仓库名称
                departmentCode: '',//部门标识
            },
            WareHouseData: [],
            dialogTableVisible: false,
            formLabelWidth: '70px',
            employeeId: '',

            departmentCode:'',
            /** 分页条数 */
            pageSize: 10,
            /** 分页页码 */
            pageNum: 1,

            /** 数据长度 */
            total: 0,
            /** 多选数据 */
            multipleSelection: [],
            // 要绑定的人员信息
            bindEmp: {
                employeeId: '',//职员id
                employeeName: '',//职员姓名
                departmentCode: '',//部门id
                departmentName:'',//部门名称
                accountId:''//用户id
            }
        }
    },
    methods: {
        //打开仓库弹窗
        openDialog(id, employeeId) {
            console.log(id, employeeId);
            this.WareHouseForm.userId = id
            this.accountId = id
            this.employeeId = employeeId
            this.WareHouseData = []
            this.dialogTableVisible = true
            this.searchWareHouse(1)
        },
        //发送请求搜索仓库
        searchWareHouse(pageNum) {

            this.WareHouseForm.current = pageNum || 1;
            this.WareHouseForm.size = this.pageSize
            this.WareHouseForm.departmentCode = this.departmentCode
            pageEmployee(this.WareHouseForm).then(res => {
                this.WareHouseData = res.data.list
                this.total = res.data.total
            })
        },
        handleSizeChange(val) {
            this.pageSize = val;
            this.searchWareHouse(1);
        },
        /**
         * @method handleCurrentChange
         * @description 当前页
         */
        handleCurrentChange(val) {
            this.pageNum = val;
            this.searchWareHouse(val);
        },
        // 选中的数据
        handleSelectionChange(val, rows) {
            if (val.length > 1) {
                this.$refs.multipleTable.clearSelection()
                this.$refs.multipleTable.toggleRowSelection(val.pop())
            } else {
                this.multipleSelection = val;
            }

        },
        handleRadioClick(row) {
            console.log('选中的数据',row);
            this.bindEmp.departmentCode = row.departmentCode
            this.bindEmp.employeeId = row.outId
            this.bindEmp.employeeName = row.empFullName
            this.bindEmp.departmentName = row.departmentName
        },
        getRowKeys(row) {
            return row.id //唯一性
        },
        //绑定仓库
        handleBind() {
            //获取选中的仓库信息
          
            this.bindEmp.accountId = this.accountId
            if (!this.bindEmp.employeeName || !this.bindEmp.employeeId) {
                this.$message.error('数据')
                return fasle
            }

            
            bindDepartment(this.bindEmp).then(res => {
                this.$message.success('绑定成功')
                this.dialogTableVisible = false
                //调用父组件的方法刷新页面
                this.$emit('searchQuery')
            }).catch(err => {
                this.$message.error('绑定失败')
            })
        },
        // 点击行数据使单选框选中
        handleClick(row, column, event) {
            this.$refs.multipleTable.toggleRowSelection(row)
        },
        //关闭弹窗
        closeDialog() {
            this.WareHouseForm = {}
            this.pageNum = 1
            this.searchWareHouse()
        }
    },

    components: {
        PageManage
    },
}
</script>

<style scoped>
.el-form {
    border-bottom: 2px solid #ccc;
    padding-bottom: 20px;
    margin-bottom: 20px;
}
</style>