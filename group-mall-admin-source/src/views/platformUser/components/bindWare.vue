<template>
    <el-dialog @close="closeDialog" title="" :visible.sync="dialogTableVisible">
        <el-form :model="WareHouseForm">
            <el-row :gutter="24">
                <el-col :span="8">
                    <div class="grid-content bg-purple">
                        <el-form-item label="仓库编号" :label-width="formLabelWidth">
                            <el-input v-model="WareHouseForm.warehouseNumber" autocomplete="off"
                                width="100%"></el-input>
                        </el-form-item>
                    </div>
                </el-col>
                <el-col :span="8">
                    <div class="grid-content bg-purple">
                        <el-form-item label="仓库名称" :label-width="formLabelWidth">
                            <el-input v-model="WareHouseForm.warehouseFullName" autocomplete="off"></el-input>
                        </el-form-item>
                    </div>
                </el-col>
                <el-col :span="8">
                    <div class="grid-content bg-purple">
                        <el-form-item label="仓库地址" :label-width="formLabelWidth">
                            <el-input v-model="WareHouseForm.warehouseAddress" autocomplete="off"></el-input>
                        </el-form-item>
                    </div>
                </el-col>
                <el-col :span="8" style="margin:10px 0 0 0;">
                    <el-button type="primary" @click="searchWareHouse(1)">搜索</el-button>
                </el-col>

            </el-row>


        </el-form>
        <!-- @selection-change="handleSelectionChange" -->
        <el-table  ref="multipleTable" tooltip-effect="dark" :data="WareHouseData"
          >
            <el-table-column label width="35">
                <template slot-scope="scope">
                    <el-radio :label="scope.row.classCode" v-model="stockCode"
                        @click.native="handleRadioClick(scope.row)">&nbsp;</el-radio>
                </template>
            </el-table-column>
            <el-table-column property="warehouseNumber" label="仓库编号" width="150"></el-table-column>
            <el-table-column property="warehouseFullName" label="仓库名称"></el-table-column>
            <el-table-column property="warehouseAddress" label="仓库地址" width="200"></el-table-column>
        </el-table>
        <el-button style="margin-top: 20px;" type="primary" @click="handleBind">绑定</el-button>
        <PageManage :pageSize="pageSize" :pageNum="pageNum" :total="total" @handleSizeChange="handleSizeChange"
            @handleCurrentChange="handleCurrentChange" class="PageManage" />

    </el-dialog>

</template>

<script>
import { bindWareHouse, bindEmployee } from '@/api/employee/employee'
import PageManage from '@/components/PageManage.vue';
export default {
    //点击编辑时弹出的仓库弹窗
    name: 'ware',
    data() {
        return {
            dialogTableVisible: true,
            WareHouseForm: {
                warehouseNumber: '',//仓库编号
                warehouseFullName: '',//仓库名称
                warehouseAddress: '',//仓库地址
            },
            WareHouseData: [],
            dialogTableVisible: false,
            formLabelWidth: '80px',
            /** 分页条数 */
            pageSize: 10,
            /** 分页页码 */
            pageNum: 1,

            /** 数据长度 */
            total: 0,
            /** 多选数据 */
            multipleSelection: [],
            // 要绑定的人员信息
            bindEmp: {
                employeeId: '',
                stockCode: '',
                stockName: ''
            },
            employeeId:'',
            stockCode:'',
            warehouseFullName:''
        }
    },
    methods: {
        //打开仓库弹窗
        openDialog(id,stockCode) {
            console.log("打开仓库弹窗", id,stockCode)
            this.employeeId = id
            this.dialogTableVisible = true
            this.stockCode = stockCode
            this.searchWareHouse(1)
        },
        //发送请求搜索仓库
        searchWareHouse(pageNum) {
            this.WareHouseForm.current = pageNum || 1;
            this.WareHouseForm.size = this.pageSize
            bindWareHouse(this.WareHouseForm).then(res => {
                this.WareHouseData = res.data.list
                this.total = res.data.total
            })
        },
        handleRadioClick(row){
            console.log(row);
            
            this.bindEmp.stockCode = row.classCode
            this.bindEmp.stockName = row.warehouseFullName
        },
        handleSizeChange(val) {
            this.pageSize = val;
            this.searchWareHouse(1);
        },

        /**
         * @method handleCurrentChange
         * @description 当前页
         */
        handleCurrentChange(val) {
            this.pageNum = val;
            this.searchWareHouse(val);
        },

       
        //绑定仓库
        handleBind() {
            this.bindEmp.accountId = this.employeeId //this.employeeId是父页面传递过来的id
            console.log("发送的请求数据", this.bindEmp);
            // //发送请求绑定仓库
            bindEmployee(this.bindEmp).then(res => {
                this.$message.success('绑定成功')
                this.dialogTableVisible = false
                this.searchWareHouse()
                //调用父组件方法刷新页面
                this.$emit('searchQuery')
            }).catch(err => {
                this.$message.error('绑定失败')
            })
        },
        //关闭弹窗
        closeDialog() {
            this.WareHouseForm = {}
            this.pageNum = 1
            this.searchWareHouse()
        }
    },
    components: {
        PageManage
    },
   
}
</script>

<style scoped>
.el-form {
    border-bottom: 2px solid #ccc;
    padding-bottom: 20px;
    margin-bottom: 20px;
}
</style>