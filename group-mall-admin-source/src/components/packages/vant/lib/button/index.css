.van-button{position:relative;display:inline-block;box-sizing:border-box;height:44px;margin:0;padding:0;font-size:16px;line-height:42px;text-align:center;border-radius:2px;cursor:pointer;-webkit-transition:opacity .2s;transition:opacity .2s;-webkit-appearance:none;-webkit-text-size-adjust:100%}.van-button::before{position:absolute;top:50%;left:50%;width:100%;height:100%;background-color:#000;border:inherit;border-color:#000;border-radius:inherit;-webkit-transform:translate(-50%,-50%);transform:translate(-50%,-50%);opacity:0;content:' '}.van-button:active::before{opacity:.1}.van-button--disabled::before,.van-button--loading::before{display:none}.van-button--default{color:#323233;background-color:#fff;border:1px solid #ebedf0}.van-button--primary{color:#fff;background-color:#07c160;border:1px solid #07c160}.van-button--info{color:#fff;background-color:#1989fa;border:1px solid #1989fa}.van-button--danger{color:#fff;background-color:#ee0a24;border:1px solid #ee0a24}.van-button--warning{color:#fff;background-color:#ff976a;border:1px solid #ff976a}.van-button--plain{background-color:#fff}.van-button--plain.van-button--primary{color:#07c160}.van-button--plain.van-button--info{color:#1989fa}.van-button--plain.van-button--danger{color:#ee0a24}.van-button--plain.van-button--warning{color:#ff976a}.van-button--large{width:100%;height:50px;line-height:48px}.van-button--normal{padding:0 15px;font-size:14px}.van-button--small{min-width:60px;height:30px;padding:0 8px;font-size:12px;line-height:28px}.van-button__loading{display:inline-block;color:inherit;vertical-align:top}.van-button--mini{display:inline-block;min-width:50px;height:22px;font-size:10px;line-height:20px}.van-button--mini+.van-button--mini{margin-left:4px}.van-button--block{display:block;width:100%}.van-button--disabled{cursor:not-allowed;opacity:.5}.van-button--loading{cursor:default}.van-button--round{border-radius:999px}.van-button--square{border-radius:0}.van-button__icon{min-width:1em;font-size:1.2em;line-height:inherit;vertical-align:top}.van-button__icon+.van-button__text,.van-button__loading+.van-button__text{display:inline-block;margin-left:5px;vertical-align:top}.van-button--hairline{border-width:0}.van-button--hairline::after{border-color:inherit;border-radius:4px}.van-button--hairline.van-button--round::after{border-radius:999px}.van-button--hairline.van-button--square::after{border-radius:0}