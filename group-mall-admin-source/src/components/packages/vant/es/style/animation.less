@import './var';

@keyframes van-slide-up-enter {
  from {
    transform: translate3d(0, 100%, 0);
  }
}

@keyframes van-slide-up-leave {
  to {
    transform: translate3d(0, 100%, 0);
  }
}

@keyframes van-slide-down-enter {
  from {
    transform: translate3d(0, -100%, 0);
  }
}

@keyframes van-slide-down-leave {
  to {
    transform: translate3d(0, -100%, 0);
  }
}

@keyframes van-slide-left-enter {
  from {
    transform: translate3d(-100%, 0, 0);
  }
}

@keyframes van-slide-left-leave {
  to {
    transform: translate3d(-100%, 0, 0);
  }
}

@keyframes van-slide-right-enter {
  from {
    transform: translate3d(100%, 0, 0);
  }
}

@keyframes van-slide-right-leave {
  to {
    transform: translate3d(100%, 0, 0);
  }
}

@keyframes van-fade-in {
  from {
    opacity: 0;
  }

  to {
    opacity: 1;
  }
}

@keyframes van-fade-out {
  from {
    opacity: 1;
  }

  to {
    opacity: 0;
  }
}

@keyframes van-rotate {
  from {
    transform: rotate(0deg);
  }

  to {
    transform: rotate(360deg);
  }
}

.van-fade {
  &-enter-active {
    animation: @animation-duration-base van-fade-in both
      @animation-timing-function-enter;
  }

  &-leave-active {
    animation: @animation-duration-base van-fade-out both
      @animation-timing-function-leave;
  }
}

.van-slide-up {
  &-enter-active {
    animation: van-slide-up-enter @animation-duration-base both
      @animation-timing-function-enter;
  }

  &-leave-active {
    animation: van-slide-up-leave @animation-duration-base both
      @animation-timing-function-leave;
  }
}

.van-slide-down {
  &-enter-active {
    animation: van-slide-down-enter @animation-duration-base both
      @animation-timing-function-enter;
  }

  &-leave-active {
    animation: van-slide-down-leave @animation-duration-base both
      @animation-timing-function-leave;
  }
}

.van-slide-left {
  &-enter-active {
    animation: van-slide-left-enter @animation-duration-base both
      @animation-timing-function-enter;
  }

  &-leave-active {
    animation: van-slide-left-leave @animation-duration-base both
      @animation-timing-function-leave;
  }
}

.van-slide-right {
  &-enter-active {
    animation: van-slide-right-enter @animation-duration-base both
      @animation-timing-function-enter;
  }

  &-leave-active {
    animation: van-slide-right-leave @animation-duration-base both
      @animation-timing-function-leave;
  }
}
