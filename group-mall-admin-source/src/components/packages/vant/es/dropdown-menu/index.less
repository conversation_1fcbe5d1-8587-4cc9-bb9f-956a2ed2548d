@import '../style/var';

.van-dropdown-menu {
  display: flex;
  height: @dropdown-menu-height;
  background-color: @dropdown-menu-background-color;
  user-select: none;

  &__item {
    display: flex;
    flex: 1;
    align-items: center;
    justify-content: center;
    min-width: 0; // hack for flex ellipsis
    cursor: pointer;

    &:active {
      opacity: @active-opacity;
    }

    &--disabled {
      &:active {
        opacity: 1;
      }

      .van-dropdown-menu__title {
        color: @dropdown-menu-title-disabled-text-color;
      }
    }
  }

  &__title {
    position: relative;
    box-sizing: border-box;
    max-width: 100%;
    padding: @dropdown-menu-title-padding;
    color: @dropdown-menu-title-text-color;
    font-size: @dropdown-menu-title-font-size;
    line-height: @dropdown-menu-title-line-height;

    &::after {
      position: absolute;
      top: 50%;
      right: -4px;
      margin-top: -5px;
      border: 3px solid;
      border-color: transparent transparent currentColor currentColor;
      transform: rotate(-45deg);
      opacity: 0.8;
      content: '';
    }

    &--active {
      color: @dropdown-menu-title-active-text-color;
    }

    &--down {
      &::after {
        margin-top: -1px;
        transform: rotate(135deg);
      }
    }
  }
}
